"""
向量数据库管理器
"""

from typing import List, Dict, Any, Optional
from loguru import logger
from .chroma_client import ChromaDBClient
from .embedding_client import EmbeddingClient


class VectorDBManager:
    """向量数据库管理器"""
    
    def __init__(self):
        self.chroma_client = ChromaDBClient()
        self.embedding_client = EmbeddingClient()
    
    def add_apis(self, apis: List[Dict[str, Any]]) -> bool:
        """
        添加API到向量数据库
        
        Args:
            apis: API列表
            
        Returns:
            bool: 是否成功
        """
        try:
            if not apis:
                logger.warning("没有API需要添加")
                return True
            
            logger.info(f"开始添加 {len(apis)} 个API到向量数据库")
            
            # 添加到ChromaDB（ChromaDB会自动处理嵌入）
            success = self.chroma_client.add_api_documents(apis)
            
            if success:
                logger.info(f"成功添加 {len(apis)} 个API到向量数据库")
            else:
                logger.error("添加API到向量数据库失败")
            
            return success
            
        except Exception as e:
            logger.error(f"添加API到向量数据库失败: {e}")
            return False
    
    def search_apis(self, query: str, n_results: int = 5) -> List[Dict[str, Any]]:
        """
        搜索相关API
        
        Args:
            query: 搜索查询
            n_results: 返回结果数量
            
        Returns:
            List[Dict]: 搜索结果
        """
        try:
            logger.info(f"搜索API: {query}")
            
            # 使用ChromaDB搜索
            results = self.chroma_client.search_apis(query, n_results)
            
            # 格式化结果
            formatted_results = []
            for result in results:
                metadata = result.get("metadata", {})
                formatted_result = {
                    "api_id": metadata.get("api_id", ""),
                    "title": metadata.get("title", ""),
                    "path": metadata.get("path", ""),
                    "method": metadata.get("method", ""),
                    "category": metadata.get("category", ""),
                    "status": metadata.get("status", ""),
                    "document": result.get("document", ""),
                    "similarity_score": 1 - result.get("distance", 1),  # 转换为相似度分数
                    "metadata": metadata
                }
                formatted_results.append(formatted_result)
            
            logger.info(f"找到 {len(formatted_results)} 个相关API")
            return formatted_results
            
        except Exception as e:
            logger.error(f"搜索API失败: {e}")
            return []
    
    def get_database_info(self) -> Dict[str, Any]:
        """获取数据库信息"""
        try:
            info = self.chroma_client.get_collection_info()
            return {
                "status": "connected",
                "collection_name": info.get("name", ""),
                "api_count": info.get("count", 0),
                "persist_directory": info.get("persist_directory", "")
            }
        except Exception as e:
            logger.error(f"获取数据库信息失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
    
    def clear_database(self) -> bool:
        """清空数据库"""
        try:
            logger.info("开始清空向量数据库")
            success = self.chroma_client.clear_collection()
            
            if success:
                logger.info("成功清空向量数据库")
            else:
                logger.error("清空向量数据库失败")
            
            return success
            
        except Exception as e:
            logger.error(f"清空向量数据库失败: {e}")
            return False
    
    def batch_search_apis(self, queries: List[str], n_results: int = 5) -> Dict[str, List[Dict[str, Any]]]:
        """
        批量搜索API
        
        Args:
            queries: 查询列表
            n_results: 每个查询返回的结果数量
            
        Returns:
            Dict[str, List[Dict]]: 查询结果字典
        """
        try:
            results = {}
            for query in queries:
                results[query] = self.search_apis(query, n_results)
            
            return results
            
        except Exception as e:
            logger.error(f"批量搜索API失败: {e}")
            return {}
    
    def get_api_by_id(self, api_id: str) -> Optional[Dict[str, Any]]:
        """
        根据ID获取API信息
        
        Args:
            api_id: API ID
            
        Returns:
            Optional[Dict]: API信息
        """
        try:
            # 使用精确匹配搜索
            results = self.chroma_client.collection.get(
                ids=[api_id],
                include=["documents", "metadatas"]
            )
            
            if results["documents"] and results["documents"][0]:
                metadata = results["metadatas"][0]
                return {
                    "api_id": metadata.get("api_id", ""),
                    "title": metadata.get("title", ""),
                    "path": metadata.get("path", ""),
                    "method": metadata.get("method", ""),
                    "category": metadata.get("category", ""),
                    "status": metadata.get("status", ""),
                    "document": results["documents"][0],
                    "metadata": metadata
                }
            
            return None
            
        except Exception as e:
            logger.error(f"根据ID获取API失败: {e}")
            return None
