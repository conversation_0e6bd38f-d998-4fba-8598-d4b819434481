# RestAgent - 调用REST API 工具的智能体应用

# 功能描述
## 1. 向量数据库
1. 调用接口管理工具-Yapi API,获取某个项目下的所有Rest接口。
2. 把接口向量化存到向量数据库中。
3. 通过向量数据库，实现接口的搜索，返回Rest接口的详细定义。
4. 向量数据库使用 chromadb

## 2. Agent - 智能助手
1. 获取用户输入，并把用户输入向量化。
2. 通过向量数据库，搜索出最匹配的Rest接口。
3. 把搜索出的Rest接口做为工具，和用户请求一起发送给大模型。
4. 根据大模型返回的结果，解析返回的工具。
5. 把解析出的工具，调用Rest接口，返回结果。
6. 把工具结果附加到message中，再次请求大模型，直到大模型不再有工具调用的输出消息。

## 3. UI
1. 实现一个WEB UI，可以与"智能助手"对话，并渲染“智能助手”返回的结果。
2. 支持图表的渲染。
## 4. 系统配置
1. 可以配置yapi 地址、用户密码信息
2. 可配置项目Rest服务的访问授权信息
3. 可配置大模型地址、模型名称、嵌入模型地址、嵌入模型名称
4. 